// gerrit_client.cpp: 定义应用程序的入口点。
//

#include "gerrit_client.h"
#include "core/obj_helper.h"
#include "ui/dc_env.h"
#include "ui/dc_surface.h"

using namespace std;

// Provides the entry point to the application.
INT WINAPI wWinMain(_In_ HINSTANCE hInstance, _In_opt_ HINSTANCE, _In_ LPWSTR, _In_ INT)
{
	obj_init_key_map();
	Easy_Object::TypeSystemInit();
	DC_Env dc_inst(hInstance, Easy_Object::get_root());
	assert(SUCCEEDED(dc_inst.Initialize()));
	Easy_Object visual1 = dc_inst.makeVisual(dc_inst.getRootVisual());
	Easy_Object surface1 = dc_inst.createSurfaceForVisual(visual1, 100, 100);
	DC_Surface_Helper surface_helper(surface1);
	D2D1::ColorF color(0.6f, 1.0f, 1.0f, 0.5f);
	Rect_Data rect_data = {0, 0, 100, 100, color};
	surface_helper.addRect("rect1", rect_data);
	surface_helper.compile();
	return dc_inst.Run();
}

