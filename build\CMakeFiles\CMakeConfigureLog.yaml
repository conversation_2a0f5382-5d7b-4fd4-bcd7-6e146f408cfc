
---
events:
  -
    kind: "message-v1"
    backtrace:
      - "D:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.28/Modules/CMakeDetermineSystem.cmake:233 (message)"
      - "CMakeLists.txt:18 (project)"
    message: |
      The system is: Windows - 10.0.22631 - AMD64
  -
    kind: "message-v1"
    backtrace:
      - "D:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.28/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "D:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.28/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "D:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.28/Modules/CMakeDetermineCCompiler.cmake:123 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:18 (project)"
    message: |
      Compiling the C compiler identification source file "CMakeCCompilerId.c" succeeded.
      Compiler:  
      Build flags: 
      Id flags:  
      
      The output was:
      0
      閫傜敤浜?.NET Framework MSBuild 鐗堟湰 17.10.4+10fbfbf2e
      鐢熸垚鍚姩鏃堕棿涓?2025/6/15 16:56:43銆?
      
      鑺傜偣 1 涓婄殑椤圭洰鈥淒:\\git\\gerrit_client\\build\\CMakeFiles\\3.28.3-msvc11\\CompilerIdC\\CompilerIdC.vcxproj鈥?榛樿鐩爣)銆?
      PrepareForBuild:
        姝ｅ湪鍒涘缓鐩綍鈥淒ebug\\鈥濄€?
        宸插惎鐢ㄧ粨鏋勫寲杈撳嚭銆傜紪璇戝櫒璇婃柇鐨勬牸寮忚缃皢鍙嶆槧閿欒灞傛缁撴瀯銆傛湁鍏宠缁嗕俊鎭紝璇峰弬闃?https://aka.ms/cpp/structured-output銆?
        姝ｅ湪鍒涘缓鐩綍鈥淒ebug\\CompilerIdC.tlog\\鈥濄€?
      InitializeBuildStatus:
        姝ｅ湪鍒涘缓鈥淒ebug\\CompilerIdC.tlog\\unsuccessfulbuild鈥濓紝鍥犱负宸叉寚瀹氣€淎lwaysCreate鈥濄€?
        姝ｅ湪瀵光€淒ebug\\CompilerIdC.tlog\\unsuccessfulbuild鈥濇墽琛?Touch 浠诲姟銆?
      VcpkgCheckManifestRoot:
        The vcpkg manifest was disabled, but we found a manifest file in D:\\git\\gerrit_client\\. You may want to enable vcpkg manifests in your properties page or pass /p:VcpkgEnableManifest=true to the msbuild invocation.
      ClCompile:
        D:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.40.33807\\bin\\HostX64\\x86\\CL.exe /c /nologo /W0 /WX- /diagnostics:column /Od /Oy- /D _MBCS /Gm- /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"Debug\\\\" /Fd"Debug\\vc143.pdb" /external:W0 /Gd /TC /analyze- /FC /errorReport:queue CMakeCCompilerId.c
        CMakeCCompilerId.c
      Link:
        D:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.40.33807\\bin\\HostX64\\x86\\link.exe /ERRORREPORT:QUEUE /OUT:".\\CompilerIdC.exe" /INCREMENTAL:NO /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib comdlg32.lib advapi32.lib shell32.lib ole32.lib oleaut32.lib uuid.lib odbc32.lib odbccp32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /PDB:".\\CompilerIdC.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:".\\CompilerIdC.lib" /MACHINE:X86 /SAFESEH Debug\\CMakeCCompilerId.obj
        CompilerIdC.vcxproj -> D:\\git\\gerrit_client\\build\\CMakeFiles\\3.28.3-msvc11\\CompilerIdC\\CompilerIdC.exe
      PostBuildEvent:
        for %%i in (cl.exe) do @echo CMAKE_C_COMPILER=%%~$PATH:i
        :VCEnd
        CMAKE_C_COMPILER=D:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.40.33807\\bin\\Hostx64\\x86\\cl.exe
      FinalizeBuildStatus:
        姝ｅ湪鍒犻櫎鏂囦欢鈥淒ebug\\CompilerIdC.tlog\\unsuccessfulbuild鈥濄€?
        姝ｅ湪瀵光€淒ebug\\CompilerIdC.tlog\\CompilerIdC.lastbuildstate鈥濇墽琛?Touch 浠诲姟銆?
      宸插畬鎴愮敓鎴愰」鐩€淒:\\git\\gerrit_client\\build\\CMakeFiles\\3.28.3-msvc11\\CompilerIdC\\CompilerIdC.vcxproj鈥?榛樿鐩爣)鐨勬搷浣溿€?
      
      宸叉垚鍔熺敓鎴愩€?
          0 涓鍛?
          0 涓敊璇?
      
      宸茬敤鏃堕棿 00:00:01.67
      
      
      Compilation of the C compiler identification source "CMakeCCompilerId.c" produced "CompilerIdC.exe"
      
      Compilation of the C compiler identification source "CMakeCCompilerId.c" produced "CompilerIdC.vcxproj"
      
      The C compiler identification is MSVC, found in:
        D:/git/gerrit_client/build/CMakeFiles/3.28.3-msvc11/CompilerIdC/CompilerIdC.exe
      
  -
    kind: "message-v1"
    backtrace:
      - "D:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.28/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "D:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.28/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "D:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.28/Modules/CMakeDetermineCXXCompiler.cmake:126 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:18 (project)"
    message: |
      Compiling the CXX compiler identification source file "CMakeCXXCompilerId.cpp" succeeded.
      Compiler:  
      Build flags: 
      Id flags:  
      
      The output was:
      0
      閫傜敤浜?.NET Framework MSBuild 鐗堟湰 17.10.4+10fbfbf2e
      鐢熸垚鍚姩鏃堕棿涓?2025/6/15 16:56:45銆?
      
      鑺傜偣 1 涓婄殑椤圭洰鈥淒:\\git\\gerrit_client\\build\\CMakeFiles\\3.28.3-msvc11\\CompilerIdCXX\\CompilerIdCXX.vcxproj鈥?榛樿鐩爣)銆?
      PrepareForBuild:
        姝ｅ湪鍒涘缓鐩綍鈥淒ebug\\鈥濄€?
        宸插惎鐢ㄧ粨鏋勫寲杈撳嚭銆傜紪璇戝櫒璇婃柇鐨勬牸寮忚缃皢鍙嶆槧閿欒灞傛缁撴瀯銆傛湁鍏宠缁嗕俊鎭紝璇峰弬闃?https://aka.ms/cpp/structured-output銆?
        姝ｅ湪鍒涘缓鐩綍鈥淒ebug\\CompilerIdCXX.tlog\\鈥濄€?
      InitializeBuildStatus:
        姝ｅ湪鍒涘缓鈥淒ebug\\CompilerIdCXX.tlog\\unsuccessfulbuild鈥濓紝鍥犱负宸叉寚瀹氣€淎lwaysCreate鈥濄€?
        姝ｅ湪瀵光€淒ebug\\CompilerIdCXX.tlog\\unsuccessfulbuild鈥濇墽琛?Touch 浠诲姟銆?
      VcpkgCheckManifestRoot:
        The vcpkg manifest was disabled, but we found a manifest file in D:\\git\\gerrit_client\\. You may want to enable vcpkg manifests in your properties page or pass /p:VcpkgEnableManifest=true to the msbuild invocation.
      ClCompile:
        D:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.40.33807\\bin\\HostX64\\x86\\CL.exe /c /nologo /W0 /WX- /diagnostics:column /Od /Oy- /D _MBCS /Gm- /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"Debug\\\\" /Fd"Debug\\vc143.pdb" /external:W0 /Gd /TP /analyze- /FC /errorReport:queue CMakeCXXCompilerId.cpp
        CMakeCXXCompilerId.cpp
      Link:
        D:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.40.33807\\bin\\HostX64\\x86\\link.exe /ERRORREPORT:QUEUE /OUT:".\\CompilerIdCXX.exe" /INCREMENTAL:NO /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib comdlg32.lib advapi32.lib shell32.lib ole32.lib oleaut32.lib uuid.lib odbc32.lib odbccp32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /PDB:".\\CompilerIdCXX.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:".\\CompilerIdCXX.lib" /MACHINE:X86 /SAFESEH Debug\\CMakeCXXCompilerId.obj
        CompilerIdCXX.vcxproj -> D:\\git\\gerrit_client\\build\\CMakeFiles\\3.28.3-msvc11\\CompilerIdCXX\\CompilerIdCXX.exe
      PostBuildEvent:
        for %%i in (cl.exe) do @echo CMAKE_CXX_COMPILER=%%~$PATH:i
        :VCEnd
        CMAKE_CXX_COMPILER=D:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.40.33807\\bin\\Hostx64\\x86\\cl.exe
      FinalizeBuildStatus:
        姝ｅ湪鍒犻櫎鏂囦欢鈥淒ebug\\CompilerIdCXX.tlog\\unsuccessfulbuild鈥濄€?
        姝ｅ湪瀵光€淒ebug\\CompilerIdCXX.tlog\\CompilerIdCXX.lastbuildstate鈥濇墽琛?Touch 浠诲姟銆?
      宸插畬鎴愮敓鎴愰」鐩€淒:\\git\\gerrit_client\\build\\CMakeFiles\\3.28.3-msvc11\\CompilerIdCXX\\CompilerIdCXX.vcxproj鈥?榛樿鐩爣)鐨勬搷浣溿€?
      
      宸叉垚鍔熺敓鎴愩€?
          0 涓鍛?
          0 涓敊璇?
      
      宸茬敤鏃堕棿 00:00:00.88
      
      
      Compilation of the CXX compiler identification source "CMakeCXXCompilerId.cpp" produced "CompilerIdCXX.exe"
      
      Compilation of the CXX compiler identification source "CMakeCXXCompilerId.cpp" produced "CompilerIdCXX.vcxproj"
      
      The CXX compiler identification is MSVC, found in:
        D:/git/gerrit_client/build/CMakeFiles/3.28.3-msvc11/CompilerIdCXX/CompilerIdCXX.exe
      
  -
    kind: "try_compile-v1"
    backtrace:
      - "D:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.28/Modules/CMakeDetermineCompilerABI.cmake:57 (try_compile)"
      - "D:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.28/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:18 (project)"
    checks:
      - "Detecting C compiler ABI info"
    directories:
      source: "D:/git/gerrit_client/build/CMakeFiles/CMakeScratch/TryCompile-ugsug5"
      binary: "D:/git/gerrit_client/build/CMakeFiles/CMakeScratch/TryCompile-ugsug5"
    cmakeVariables:
      CMAKE_C_FLAGS: "/DWIN32 /D_WINDOWS /W3"
      CMAKE_C_FLAGS_DEBUG: "/MDd /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:X86"
      CMAKE_MSVC_DEBUG_INFORMATION_FORMAT: "$<IF:$<AND:$<C_COMPILER_ID:MSVC>,$<CXX_COMPILER_ID:MSVC>>,$<$<CONFIG:Debug,RelWithDebInfo>:EditAndContinue>,$<$<CONFIG:Debug,RelWithDebInfo>:ProgramDatabase>>"
    buildResult:
      variable: "CMAKE_C_ABI_COMPILED"
      cached: true
      stdout: |
        Change Dir: 'D:/git/gerrit_client/build/CMakeFiles/CMakeScratch/TryCompile-ugsug5'
        
        Run Build Command(s): "D:/Program Files/Microsoft Visual Studio/2022/Community/MSBuild/Current/Bin/amd64/MSBuild.exe" cmTC_10343.vcxproj /p:Configuration=Debug /p:Platform=win32 /p:VisualStudioVersion=17.0 /v:n
        閫傜敤浜?.NET Framework MSBuild 鐗堟湰 17.10.4+10fbfbf2e
        鐢熸垚鍚姩鏃堕棿涓?2025/6/15 16:56:46銆?
        
        鑺傜偣 1 涓婄殑椤圭洰鈥淒:\\git\\gerrit_client\\build\\CMakeFiles\\CMakeScratch\\TryCompile-ugsug5\\cmTC_10343.vcxproj鈥?榛樿鐩爣)銆?
        PrepareForBuild:
          姝ｅ湪鍒涘缓鐩綍鈥渃mTC_10343.dir\\Debug\\鈥濄€?
          宸插惎鐢ㄧ粨鏋勫寲杈撳嚭銆傜紪璇戝櫒璇婃柇鐨勬牸寮忚缃皢鍙嶆槧閿欒灞傛缁撴瀯銆傛湁鍏宠缁嗕俊鎭紝璇峰弬闃?https://aka.ms/cpp/structured-output銆?
          姝ｅ湪鍒涘缓鐩綍鈥淒:\\git\\gerrit_client\\build\\CMakeFiles\\CMakeScratch\\TryCompile-ugsug5\\Debug\\鈥濄€?
          姝ｅ湪鍒涘缓鐩綍鈥渃mTC_10343.dir\\Debug\\cmTC_10343.tlog\\鈥濄€?
        InitializeBuildStatus:
          姝ｅ湪鍒涘缓鈥渃mTC_10343.dir\\Debug\\cmTC_10343.tlog\\unsuccessfulbuild鈥濓紝鍥犱负宸叉寚瀹氣€淎lwaysCreate鈥濄€?
          姝ｅ湪瀵光€渃mTC_10343.dir\\Debug\\cmTC_10343.tlog\\unsuccessfulbuild鈥濇墽琛?Touch 浠诲姟銆?
        VcpkgCheckManifestRoot:
          The vcpkg manifest was disabled, but we found a manifest file in D:\\git\\gerrit_client\\. You may want to enable vcpkg manifests in your properties page or pass /p:VcpkgEnableManifest=true to the msbuild invocation.
        ClCompile:
          D:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.40.33807\\bin\\HostX64\\x86\\CL.exe /c /Zi /W3 /WX- /diagnostics:column /Od /Ob0 /Oy- /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /Gm- /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_10343.dir\\Debug\\\\" /Fd"cmTC_10343.dir\\Debug\\vc143.pdb" /external:W3 /Gd /TC /analyze- /errorReport:queue "D:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\Common7\\IDE\\CommonExtensions\\Microsoft\\CMake\\CMake\\share\\cmake-3.28\\Modules\\CMakeCCompilerABI.c"
          鐢ㄤ簬 x86 鐨?Microsoft (R) C/C++ 浼樺寲缂栬瘧鍣?19.40.33812 鐗?
          鐗堟潈鎵€鏈?C) Microsoft Corporation銆備繚鐣欐墍鏈夋潈鍒┿€?
          cl /c /Zi /W3 /WX- /diagnostics:column /Od /Ob0 /Oy- /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /Gm- /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_10343.dir\\Debug\\\\" /Fd"cmTC_10343.dir\\Debug\\vc143.pdb" /external:W3 /Gd /TC /analyze- /errorReport:queue "D:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\Common7\\IDE\\CommonExtensions\\Microsoft\\CMake\\CMake\\share\\cmake-3.28\\Modules\\CMakeCCompilerABI.c"
          CMakeCCompilerABI.c
        Link:
          D:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.40.33807\\bin\\HostX64\\x86\\link.exe /ERRORREPORT:QUEUE /OUT:"D:\\git\\gerrit_client\\build\\CMakeFiles\\CMakeScratch\\TryCompile-ugsug5\\Debug\\cmTC_10343.exe" /INCREMENTAL /ILK:"cmTC_10343.dir\\Debug\\cmTC_10343.ilk" /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"D:/git/gerrit_client/build/CMakeFiles/CMakeScratch/TryCompile-ugsug5/Debug/cmTC_10343.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:"D:/git/gerrit_client/build/CMakeFiles/CMakeScratch/TryCompile-ugsug5/Debug/cmTC_10343.lib" /MACHINE:X86 /SAFESEH  /machine:X86 cmTC_10343.dir\\Debug\\CMakeCCompilerABI.obj
          cmTC_10343.vcxproj -> D:\\git\\gerrit_client\\build\\CMakeFiles\\CMakeScratch\\TryCompile-ugsug5\\Debug\\cmTC_10343.exe
        FinalizeBuildStatus:
          姝ｅ湪鍒犻櫎鏂囦欢鈥渃mTC_10343.dir\\Debug\\cmTC_10343.tlog\\unsuccessfulbuild鈥濄€?
          姝ｅ湪瀵光€渃mTC_10343.dir\\Debug\\cmTC_10343.tlog\\cmTC_10343.lastbuildstate鈥濇墽琛?Touch 浠诲姟銆?
        宸插畬鎴愮敓鎴愰」鐩€淒:\\git\\gerrit_client\\build\\CMakeFiles\\CMakeScratch\\TryCompile-ugsug5\\cmTC_10343.vcxproj鈥?榛樿鐩爣)鐨勬搷浣溿€?
        
        宸叉垚鍔熺敓鎴愩€?
            0 涓鍛?
            0 涓敊璇?
        
        宸茬敤鏃堕棿 00:00:00.92
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "D:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.28/Modules/CMakeDetermineCompilerABI.cmake:57 (try_compile)"
      - "D:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.28/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:18 (project)"
    checks:
      - "Detecting CXX compiler ABI info"
    directories:
      source: "D:/git/gerrit_client/build/CMakeFiles/CMakeScratch/TryCompile-jfidfb"
      binary: "D:/git/gerrit_client/build/CMakeFiles/CMakeScratch/TryCompile-jfidfb"
    cmakeVariables:
      CMAKE_CXX_FLAGS: "/DWIN32 /D_WINDOWS /W3 /GR /EHsc"
      CMAKE_CXX_FLAGS_DEBUG: "/MDd /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:X86"
      CMAKE_MSVC_DEBUG_INFORMATION_FORMAT: "$<IF:$<AND:$<C_COMPILER_ID:MSVC>,$<CXX_COMPILER_ID:MSVC>>,$<$<CONFIG:Debug,RelWithDebInfo>:EditAndContinue>,$<$<CONFIG:Debug,RelWithDebInfo>:ProgramDatabase>>"
    buildResult:
      variable: "CMAKE_CXX_ABI_COMPILED"
      cached: true
      stdout: |
        Change Dir: 'D:/git/gerrit_client/build/CMakeFiles/CMakeScratch/TryCompile-jfidfb'
        
        Run Build Command(s): "D:/Program Files/Microsoft Visual Studio/2022/Community/MSBuild/Current/Bin/amd64/MSBuild.exe" cmTC_9adc8.vcxproj /p:Configuration=Debug /p:Platform=win32 /p:VisualStudioVersion=17.0 /v:n
        閫傜敤浜?.NET Framework MSBuild 鐗堟湰 17.10.4+10fbfbf2e
        鐢熸垚鍚姩鏃堕棿涓?2025/6/15 16:56:48銆?
        
        鑺傜偣 1 涓婄殑椤圭洰鈥淒:\\git\\gerrit_client\\build\\CMakeFiles\\CMakeScratch\\TryCompile-jfidfb\\cmTC_9adc8.vcxproj鈥?榛樿鐩爣)銆?
        PrepareForBuild:
          姝ｅ湪鍒涘缓鐩綍鈥渃mTC_9adc8.dir\\Debug\\鈥濄€?
          宸插惎鐢ㄧ粨鏋勫寲杈撳嚭銆傜紪璇戝櫒璇婃柇鐨勬牸寮忚缃皢鍙嶆槧閿欒灞傛缁撴瀯銆傛湁鍏宠缁嗕俊鎭紝璇峰弬闃?https://aka.ms/cpp/structured-output銆?
          姝ｅ湪鍒涘缓鐩綍鈥淒:\\git\\gerrit_client\\build\\CMakeFiles\\CMakeScratch\\TryCompile-jfidfb\\Debug\\鈥濄€?
          姝ｅ湪鍒涘缓鐩綍鈥渃mTC_9adc8.dir\\Debug\\cmTC_9adc8.tlog\\鈥濄€?
        InitializeBuildStatus:
          姝ｅ湪鍒涘缓鈥渃mTC_9adc8.dir\\Debug\\cmTC_9adc8.tlog\\unsuccessfulbuild鈥濓紝鍥犱负宸叉寚瀹氣€淎lwaysCreate鈥濄€?
          姝ｅ湪瀵光€渃mTC_9adc8.dir\\Debug\\cmTC_9adc8.tlog\\unsuccessfulbuild鈥濇墽琛?Touch 浠诲姟銆?
        VcpkgCheckManifestRoot:
          The vcpkg manifest was disabled, but we found a manifest file in D:\\git\\gerrit_client\\. You may want to enable vcpkg manifests in your properties page or pass /p:VcpkgEnableManifest=true to the msbuild invocation.
        ClCompile:
          D:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.40.33807\\bin\\HostX64\\x86\\CL.exe /c /Zi /W3 /WX- /diagnostics:column /Od /Ob0 /Oy- /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /Gm- /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /GR /Fo"cmTC_9adc8.dir\\Debug\\\\" /Fd"cmTC_9adc8.dir\\Debug\\vc143.pdb" /external:W3 /Gd /TP /analyze- /errorReport:queue "D:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\Common7\\IDE\\CommonExtensions\\Microsoft\\CMake\\CMake\\share\\cmake-3.28\\Modules\\CMakeCXXCompilerABI.cpp"
          鐢ㄤ簬 x86 鐨?Microsoft (R) C/C++ 浼樺寲缂栬瘧鍣?19.40.33812 鐗?
          鐗堟潈鎵€鏈?C) Microsoft Corporation銆備繚鐣欐墍鏈夋潈鍒┿€?
          cl /c /Zi /W3 /WX- /diagnostics:column /Od /Ob0 /Oy- /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /Gm- /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /GR /Fo"cmTC_9adc8.dir\\Debug\\\\" /Fd"cmTC_9adc8.dir\\Debug\\vc143.pdb" /external:W3 /Gd /TP /analyze- /errorReport:queue "D:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\Common7\\IDE\\CommonExtensions\\Microsoft\\CMake\\CMake\\share\\cmake-3.28\\Modules\\CMakeCXXCompilerABI.cpp"
          CMakeCXXCompilerABI.cpp
        Link:
          D:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.40.33807\\bin\\HostX64\\x86\\link.exe /ERRORREPORT:QUEUE /OUT:"D:\\git\\gerrit_client\\build\\CMakeFiles\\CMakeScratch\\TryCompile-jfidfb\\Debug\\cmTC_9adc8.exe" /INCREMENTAL /ILK:"cmTC_9adc8.dir\\Debug\\cmTC_9adc8.ilk" /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"D:/git/gerrit_client/build/CMakeFiles/CMakeScratch/TryCompile-jfidfb/Debug/cmTC_9adc8.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:"D:/git/gerrit_client/build/CMakeFiles/CMakeScratch/TryCompile-jfidfb/Debug/cmTC_9adc8.lib" /MACHINE:X86 /SAFESEH  /machine:X86 cmTC_9adc8.dir\\Debug\\CMakeCXXCompilerABI.obj
          cmTC_9adc8.vcxproj -> D:\\git\\gerrit_client\\build\\CMakeFiles\\CMakeScratch\\TryCompile-jfidfb\\Debug\\cmTC_9adc8.exe
        FinalizeBuildStatus:
          姝ｅ湪鍒犻櫎鏂囦欢鈥渃mTC_9adc8.dir\\Debug\\cmTC_9adc8.tlog\\unsuccessfulbuild鈥濄€?
          姝ｅ湪瀵光€渃mTC_9adc8.dir\\Debug\\cmTC_9adc8.tlog\\cmTC_9adc8.lastbuildstate鈥濇墽琛?Touch 浠诲姟銆?
        宸插畬鎴愮敓鎴愰」鐩€淒:\\git\\gerrit_client\\build\\CMakeFiles\\CMakeScratch\\TryCompile-jfidfb\\cmTC_9adc8.vcxproj鈥?榛樿鐩爣)鐨勬搷浣溿€?
        
        宸叉垚鍔熺敓鎴愩€?
            0 涓鍛?
            0 涓敊璇?
        
        宸茬敤鏃堕棿 00:00:00.96
        
      exitCode: 0
...
